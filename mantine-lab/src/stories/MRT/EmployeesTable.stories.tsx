import type { Meta, StoryObj } from '@storybook/react';
import { useMemo, useState } from 'react';
import { DataTable, type DataTableColumn } from 'mantine-datatable';
import { Button, Group, Modal, TextInput } from '@mantine/core';
import { faker } from '@faker-js/faker';

type Employee = {
  id: string;
  name: string;
  email: string;
  jobTitle: string;
  salary: number;
  startDate: string;
};

const seed = (n = 100): Employee[] =>
  Array.from({ length: n }).map(() => ({
    id: crypto.randomUUID(),
    name: faker.person.fullName(),
    email: faker.internet.email().toLowerCase(),
    jobTitle: faker.person.jobTitle(),
    salary: faker.number.int({ min: 2500, max: 25000 }),
    startDate: faker.date.past().toLocaleDateString('pt-BR'),
  }));

const meta: Meta = {
  title: 'Mantine DataTable/Employees (CRUD + Mass edit)',
  parameters: { layout: 'fullscreen' },
};
export default meta;
type Story = StoryObj;

export const FullDemo: Story = {
  render: () => {
    const [records, setRecords] = useState<Employee[]>(seed(100));
    const [selection, setSelection] = useState<string[]>([]);
    const [createOpen, setCreateOpen] = useState(false);
    const [massOpen, setMassOpen] = useState(false);
    const [form, setForm] = useState<Partial<Employee>>({});
    const [massJobTitle, setMassJobTitle] = useState('');

    const columns = useMemo<DataTableColumn<Employee>[]>(() => [
      { accessor: 'name', title: 'Nome', sortable: true, filter: 'includes' },
      { accessor: 'email', title: 'Email', sortable: true, filter: 'includes' },
      { accessor: 'jobTitle', title: 'Cargo', sortable: true, filter: 'includes' },
      { accessor: 'salary', title: 'Salário', sortable: true, render: (r) => r.salary.toLocaleString('pt-BR') },
      { accessor: 'startDate', title: 'Entrada', sortable: true },
    ], []);

    const addEmployee = () => {
      if (!form.name || !form.email || !form.jobTitle || !form.salary || !form.startDate) return;
      setRecords(prev => [{ id: crypto.randomUUID(), ...(form as Employee) }, ...prev]);
      setForm({});
      setCreateOpen(false);
    };

    const updateEmployee = (id: string, patch: Partial<Employee>) =>
      setRecords(prev => prev.map(r => r.id === id ? { ...r, ...patch } : r));

    const deleteEmployee = (id: string) =>
      setRecords(prev => prev.filter(r => r.id !== id));

    const applyMass = () => {
      if (!massJobTitle) return setMassOpen(false);
      setRecords(prev => prev.map(r => selection.includes(r.id) ? { ...r, jobTitle: massJobTitle } : r));
      setMassJobTitle('');
      setSelection([]);
      setMassOpen(false);
    };

    return (
      <>
        <Group p="sm">
          <Button onClick={() => setCreateOpen(true)}>Novo</Button>
          <Button variant="outline" disabled={!selection.length} onClick={() => setMassOpen(true)}>
            Edição em massa (cargo)
          </Button>
        </Group>

        <DataTable<Employee>
          withTableBorder
          withColumnBorders
          highlightOnHover
          striped
          records={records}
          columns={[
            ...columns,
            {
              accessor: 'actions',
              title: 'Ações',
              render: (r) => (
                <Group gap="xs">
                  <Button size="xs" variant="subtle" onClick={() => updateEmployee(r.id, { jobTitle: r.jobTitle + ' (edit)' })}>
                    Editar
                  </Button>
                  <Button size="xs" color="red" variant="subtle" onClick={() => deleteEmployee(r.id)}>
                    Remover
                  </Button>
                </Group>
              ),
            },
          ]}
          totalRecords={records.length}
          recordsPerPage={10}
          paginationColor="blue"
          page={1}
          onRowClick={() => {}}
          selectedRecords={records.filter(r => selection.includes(r.id))}
          onSelectedRecordsChange={(rows) => setSelection(rows.map(r => r.id))}
          selectionTrigger="cell"
          idAccessor="id"
          // built-ins
          sortStatus={{ columnAccessor: 'name', direction: 'asc' }}
          onSortStatusChange={() => {}}
          // enable built-in search/filter UI quickly via toolbar slot if desired
        />

        {/* Create */}
        <Modal opened={createOpen} onClose={() => setCreateOpen(false)} title="Novo colaborador">
          <Group grow>
            <TextInput label="Nome" value={form.name || ''} onChange={(e) => setForm({ ...form, name: e.currentTarget.value })}/>
            <TextInput label="Email" value={form.email || ''} onChange={(e) => setForm({ ...form, email: e.currentTarget.value })}/>
            <TextInput label="Cargo" value={form.jobTitle || ''} onChange={(e) => setForm({ ...form, jobTitle: e.currentTarget.value })}/>
            <TextInput label="Salário" value={String(form.salary ?? '')} onChange={(e) => setForm({ ...form, salary: Number(e.currentTarget.value) })}/>
            <TextInput label="Entrada" value={form.startDate || ''} onChange={(e) => setForm({ ...form, startDate: e.currentTarget.value })}/>
            <Button onClick={addEmployee}>Salvar</Button>
          </Group>
        </Modal>

        {/* Mass edit */}
        <Modal opened={massOpen} onClose={() => setMassOpen(false)} title="Edição em massa">
          <Group>
            <TextInput label="Cargo" value={massJobTitle} onChange={(e) => setMassJobTitle(e.currentTarget.value)} />
            <Button onClick={applyMass}>Aplicar</Button>
          </Group>
        </Modal>
      </>
    );
  },
};
