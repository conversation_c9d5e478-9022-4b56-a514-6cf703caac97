import type { Meta, StoryObj } from '@storybook/react';
import { useMemo, useState } from 'react';
import { DataTable, type DataTableColumn } from 'mantine-datatable';
import { Button, Group, TextInput, Text, Stack, Paper, Modal } from '@mantine/core';
import { faker } from '@faker-js/faker';
import { useDebounced } from './helpers/useDebounced';
import { api } from './helpers/api';
import { openEditEmployeeModal } from './EditEmployeeModal';


// ---------- Types & seed ----------

type Employee = {
  id: string;
  name: string;
  email: string;
  jobTitle: string;
  salary: number;
  startDate: string;
};

const seed = (n = 250): Employee[] =>
  Array.from({ length: n }).map(() => ({
    id: crypto.randomUUID(),
    name: faker.person.fullName(),
    email: faker.internet.email().toLowerCase(),
    jobTitle: faker.person.jobTitle(),
    salary: faker.number.int({ min: 2500, max: 25000 }),
    startDate: faker.date.past().toLocaleDateString('pt-BR'),
  }));


// ---------- Story ----------

const meta: Meta = {
  title: 'Mantine DataTable/Employees — Inline Edit + Debounced Search + Optimistic Save',
  parameters: { layout: 'fullscreen' },
};
export default meta;

type Story = StoryObj;

export const InlineEditDemo: Story = {
  render: () => {
    // Data
    const [records, setRecords] = useState<Employee[]>(seed());

    const optimisticUpdate = async (id: string, patch: Partial<Employee>) => {
  const prev = [...records];
  setRecords(p => p.map(r => (r.id === id ? { ...r, ...patch } : r)));
  try { await new Promise((res, rej) => setTimeout(() => (Math.random() < 0.1 ? rej() : res(null)), 400)); }
  catch { setRecords(prev); } // rollback on error
};


    // Global search (debounced)
    const [query, setQuery] = useState('');
    const q = useDebounced(query, 300);

    // Simple inline editing state (edit a single field per row for demo)
    const [editingId, setEditingId] = useState<string | null>(null);
    const [draft, setDraft] = useState('');
    const [saving, setSaving] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [curRecord, setCurRecord] = useState<Employee | null>(null);

    const onEdit = (rec: Employee) => setCurRecord(rec);

    const filtered = useMemo(() => {
      if (!q.trim()) return records;
      const needle = q.toLowerCase();
      return records.filter((r) =>
        [r.name, r.email, r.jobTitle].some((s) => s.toLowerCase().includes(needle))
      );
    }, [records, q]);

    const columns = useMemo<DataTableColumn<Employee>[]>(
      () => [
        { accessor: 'name', title: 'Nome', sortable: true },
        { accessor: 'email', title: 'Email', sortable: true },
        {
          accessor: 'jobTitle',
          title: 'Cargo',
          sortable: true,
          render: (r) =>
            editingId === r.id ? (
              <TextInput
                size="xs"
                value={draft}
                onChange={(e) => setDraft(e.currentTarget.value)}
                placeholder="Cargo"
              />
            ) : (
              <Text>{r.jobTitle}</Text>
            ),
        },
        {
          accessor: 'salary',
          title: 'Salário',
          sortable: true,
          render: (r) => r.salary.toLocaleString('pt-BR'),
        },
        { accessor: 'startDate', title: 'Entrada', sortable: true },
        {
          accessor: 'actions',
          title: 'Ações',
          render: (r) => {
            const isEditing = editingId === r.id;
            return (
              <Group gap="xs" justify="flex-start">
                {isEditing ? (
                  <>
                    <Button
                      size="xs"
                      variant="filled"
                      loading={saving}
                      onClick={async () => {
                        setSaving(true);
                        setError(null);
                        const prev = records;
                        // optimistic patch
                        setRecords((curr) =>
                          curr.map((x) => (x.id === r.id ? { ...x, jobTitle: draft } : x))
                        );
                        try {
                          await api.update(r.id, { jobTitle: draft });
                          setEditingId(null);
                        } catch (e: any) {
                          // rollback
                          setRecords(prev);
                          setError(e?.message || 'Erro ao salvar');
                        } finally {
                          setSaving(false);
                        }
                      }}
                    >
                      Salvar
                    </Button>
                    <Button
                      size="xs"
                      variant="subtle"
                      color="gray"
                      onClick={() => {
                        setEditingId(null);
                        setDraft('');
                        setError(null);
                      }}
                    >
                      Cancelar
                    </Button>
                  </>
                ) : (
                  <Button
                    size="xs"
                    variant="subtle"
                    onClick={() => {
                      setEditingId(r.id);
                      setDraft(r.jobTitle);
                      setCurRecord(r);
                      openEditEmployeeModal(r, (patch) => optimisticUpdate(r.id, patch))
                    }}
                  >
                    Editar cargo
                  </Button>
                )}
              </Group>
            );
          },
        },
      ],
      [editingId, draft, records, saving]
    );


    return (
      <Stack p="md" gap="sm">

        {/* <Modal 
          opened={!!curRecord} 
          onClose={() => setCurRecord(null)} 
          title="Editar colaborador" 
          centered 
          portalProps={{ target: document.body }}
          overlayProps={{ blur: 2 }}
          radius="md"
        >
          {curRecord && (
            <Group grow>
              <TextInput
                label="Nome"
                value={curRecord.name}
                onChange={(e) =>
                  setCurRecord(prev => prev ? { ...prev, name: e.currentTarget.value } : prev)
                }
              />
              <TextInput
                label="Email"
                value={curRecord.email}
                onChange={(e) =>
                  setCurRecord(prev => prev ? { ...prev, email: e.currentTarget.value } : prev)
                }
              />
              <TextInput
                label="Cargo"
                value={curRecord.jobTitle}
                onChange={(e) =>
                  setCurRecord(prev => prev ? { ...prev, jobTitle: e.currentTarget.value } : prev)
                }
              />
              <TextInput
                label="Salário"
                value={String(curRecord.salary)}
                onChange={(e) =>
                  setCurRecord(prev => prev ? { ...prev, salary: Number(e.currentTarget.value) } : prev)
                }
              />
              <TextInput
                label="Entrada"
                value={curRecord.startDate}
                onChange={(e) =>
                  setCurRecord(prev => prev ? { ...prev, startDate: e.currentTarget.value } : prev)
                }
              />
            </Group>
          )}
        </Modal> */}
        <Group>
          <TextInput
            placeholder="Buscar por nome, email ou cargo…"
            value={query}
            onChange={(e) => setQuery(e.currentTarget.value)}
            w={360}
          />
        </Group>

        {error && (
          <Paper p="xs" radius="sm" withBorder>
            <Text c="red.6">{error}</Text>
          </Paper>
        )}

        <DataTable<Employee>
          withTableBorder
          withColumnBorders
          highlightOnHover
          striped
          records={filtered}
          columns={columns}
          idAccessor="id"
          totalRecords={filtered.length}
          recordsPerPage={10}
          sortStatus={{ columnAccessor: 'name', direction: 'asc' }}
          onSortStatusChange={() => {}}
        />
      </Stack>
    );
  },
};
