import { modals } from '@mantine/modals';
import { Button, Group, TextInput } from '@mantine/core';

type Employee = {
  id: string;
  name: string;
  email: string;
  jobTitle: string;
  salary: number;
  startDate: string;
};

/**
 * Opens a reusable modal to edit Employee fields.
 * Keeps simple local variables for the draft (no external state required).
 * Calls onSave(patch) with only the changed fields you’ll merge in your table.
 */
export function openEditEmployeeModal(
  record: Employee,
  onSave: (patch: Partial<Employee>) => void
) {
  // local "draft" vars capture user typing
  let name = record.name;
  let email = record.email;
  let jobTitle = record.jobTitle;
  let salary = record.salary;
  let startDate = record.startDate;

  const id = modals.open({
    title: 'Editar colaborador',
    centered: true,
    children: (
      <Group align="flex-end" wrap="wrap" gap="sm">
        <TextInput label="Nome" defaultValue={name} onChange={(e) => (name = e.currentTarget.value)} />
        <TextInput label="Email" defaultValue={email} onChange={(e) => (email = e.currentTarget.value)} />
        <TextInput label="Cargo" defaultValue={jobTitle} onChange={(e) => (jobTitle = e.currentTarget.value)} />
        <TextInput
          label="Salário"
          defaultValue={String(salary)}
          onChange={(e) => (salary = Number(e.currentTarget.value))}
        />
        <TextInput label="Entrada" defaultValue={startDate} onChange={(e) => (startDate = e.currentTarget.value)} />
        <Button
          onClick={() => {
            onSave({ name, email, jobTitle, salary, startDate });
            modals.close(id);
          }}
        >
          Salvar
        </Button>
      </Group>
    ),
  });

  return id;
}
