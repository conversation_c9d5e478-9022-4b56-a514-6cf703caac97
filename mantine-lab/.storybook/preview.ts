import type { Preview } from '@storybook/react';
import { MantineProvider } from '@mantine/core';
import React from 'react';

const preview: Preview = {
  decorators: [
    (Story, ctx) => (
      <MantineProvider
        defaultColorScheme={(ctx.globals.theme as 'light'|'dark') ?? 'light'}
        theme={{ fontFamily: 'Inter, system-ui, sans-serif' }}
        withCssVariables
      >
        <Story />
      </MantineProvider>
    ),
  ],
  globalTypes: {
    theme: {
      name: 'Theme',
      description: 'Light/Dark',
      defaultValue: 'light',
      toolbar: { icon: 'circlehollow', items: ['light', 'dark'] },
    },
  },
};
export default preview;