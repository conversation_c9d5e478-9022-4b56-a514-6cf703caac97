import type { Preview } from '@storybook/react';
import React from 'react';
import { MantineProvider, ColorSchemeScript } from '@mantine/core';
import { ModalsProvider } from '@mantine/modals';
import '@mantine/core/styles.css';        // ✅ Mantine v8 global styles
import '../src/index.css';

// Light/Dark switching via Storybook globals
const preview: Preview = {
  decorators: [
    (Story, ctx) => {
      const colorScheme = (ctx.globals.theme as 'light' | 'dark') ?? 'light';
      return (
        <MantineProvider
          defaultColorScheme={colorScheme}
          withCssVariables
          withGlobalStyles
          withNormalizeCSS
          theme={{ fontFamily: 'Inter, system-ui, sans-serif' }}
        >
          <ColorSchemeScript />
          <ModalsProvider>
            <Story />
          </ModalsProvider>
        </MantineProvider>
      );
    },
  ],
  globalTypes: {
    theme: {
      name: 'Theme',
      description: 'Light/Dark',
      defaultValue: 'light',
      toolbar: { icon: 'circlehollow', items: ['light', 'dark'] },
    },
  },
  parameters: {
    backgrounds: { default: 'light' },
    controls: { expanded: true },
  },
};

export default preview;
